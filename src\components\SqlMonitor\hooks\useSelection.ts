/**
 * 通用选择管理Hook
 * 管理表格行选择状态，支持跨页面选择，支持泛型
 */

import { useState, useCallback, useEffect } from 'react';
import type { TableRowSelection } from 'antd/es/table/interface';

export interface SelectionState<T> {
  selectedRowKeys: React.Key[];
  selectedRows: T[];
}

export interface UseSelectionOptions<T> {
  /** 是否支持跨页面选择 */
  crossPage?: boolean;
  /** 初始选择的keys */
  initialSelectedKeys?: React.Key[];
  /** 选择变化回调 */
  onSelectionChange?: (selectedKeys: React.Key[], selectedRows: T[]) => void;
}

export interface UseSelectionReturn<T> {
  /** 当前页面选择状态 */
  selection: SelectionState<T>;
  /** 所有选中的行数据（跨页面） */
  allSelectedRows: Map<React.Key, T>;
  /** 表格行选择配置 */
  rowSelection: TableRowSelection<T>;
  /** 清空选择方法 */
  clearSelection: () => void;
  /** 选择所有方法 */
  selectAll: (data: T[]) => void;
  /** 取消选择所有方法 */
  unselectAll: (data: T[]) => void;
  /** 获取选中的行数据方法 */
  getSelectedRows: () => T[];
  /** 获取选中的行键方法 */
  getSelectedKeys: () => React.Key[];
  /** 获取选中数量方法 */
  getSelectedCount: () => number;
}

/**
 * 通用选择管理Hook
 */
export function useSelection<T extends { id: number | string }>(data: T[], options: UseSelectionOptions<T> = {}): UseSelectionReturn<T> {
  const { crossPage = true, initialSelectedKeys = [], onSelectionChange } = options;

  // 当前页面选择状态
  const [selection, setSelection] = useState<SelectionState<T>>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 跨页面选择状态（所有选中的行数据）
  const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, T>>(new Map());

  // 处理选择变化
  const handleSelectionChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: T[]) => {
      console.log('选择变化:', { selectedRowKeys, selectedRows });

      // 更新当前页面选择状态
      setSelection({
        selectedRowKeys,
        selectedRows,
      });

      // 如果支持跨页面选择，更新全局选择状态
      if (crossPage) {
        setAllSelectedRows(prev => {
          const newMap = new Map(prev);

          // 移除当前页面所有数据的选择状态
          data.forEach(item => {
            newMap.delete(item.id);
          });

          // 添加新选中的数据
          selectedRows.forEach(row => {
            newMap.set(row.id, row);
          });

          return newMap;
        });
      }

      // 触发回调
      if (onSelectionChange) {
        if (crossPage) {
          // 跨页面选择时，需要计算最新的所有选中数据
          // 先移除当前页面的数据，再添加新选中的数据
          const newAllSelectedRows = new Map(allSelectedRows);
          data.forEach(item => {
            newAllSelectedRows.delete(item.id);
          });
          selectedRows.forEach(row => {
            newAllSelectedRows.set(row.id, row);
          });

          const allKeys = Array.from(newAllSelectedRows.keys());
          const allRows = Array.from(newAllSelectedRows.values());
          onSelectionChange(allKeys, allRows);
        } else {
          // 单页面选择时，只传递当前页面的数据
          onSelectionChange(selectedRowKeys, selectedRows);
        }
      }
    },
    [data, crossPage, onSelectionChange, allSelectedRows]
  );

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
    setAllSelectedRows(new Map());
  }, []);

  // 选择所有
  const selectAll = useCallback(
    (dataToSelect: T[]) => {
      const keys = dataToSelect.map(item => item.id);
      handleSelectionChange(keys, dataToSelect);
    },
    [handleSelectionChange]
  );

  // 取消选择所有
  const unselectAll = useCallback(
    (dataToUnselect: T[]) => {
      if (crossPage) {
        // 跨页面模式：从全局选择中移除指定数据
        setAllSelectedRows(prev => {
          const newMap = new Map(prev);
          dataToUnselect.forEach(item => {
            newMap.delete(item.id);
          });
          return newMap;
        });
      }

      // 清空当前页面选择
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
    },
    [crossPage]
  );

  // 获取选中的行数据
  const getSelectedRows = useCallback((): T[] => {
    if (crossPage) {
      return Array.from(allSelectedRows.values());
    }
    return selection.selectedRows;
  }, [crossPage, allSelectedRows, selection.selectedRows]);

  // 获取选中的行键
  const getSelectedKeys = useCallback((): React.Key[] => {
    if (crossPage) {
      return Array.from(allSelectedRows.keys());
    }
    return selection.selectedRowKeys;
  }, [crossPage, allSelectedRows, selection.selectedRowKeys]);

  // 获取选中数量
  const getSelectedCount = useCallback((): number => {
    if (crossPage) {
      return allSelectedRows.size;
    }
    return selection.selectedRowKeys.length;
  }, [crossPage, allSelectedRows.size, selection.selectedRowKeys.length]);

  // 表格行选择配置
  const rowSelection: TableRowSelection<T> = {
    type: 'checkbox',
    selectedRowKeys: selection.selectedRowKeys,
    onChange: handleSelectionChange,
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      console.log('单行选择:', { record, selected, selectedRows, nativeEvent });
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log('全选/取消全选:', { selected, selectedRows, changeRows });
    },
    getCheckboxProps: record => ({
      name: `checkbox-${record.id}`,
    }),
  };

  // 处理初始选择的keys和外部selectedKeys变化
  useEffect(() => {
    if (initialSelectedKeys.length > 0 && data.length > 0) {
      const selectedRowsFromKeys = data.filter(item => initialSelectedKeys.includes(item.id));

      if (crossPage) {
        // 跨页面模式：重新设置全局选择状态
        const newMap = new Map<React.Key, T>();
        selectedRowsFromKeys.forEach(row => {
          newMap.set(row.id, row);
        });
        setAllSelectedRows(newMap);
      }

      // 更新当前页面选择状态
      const currentPageSelectedKeys = selectedRowsFromKeys.map(item => item.id);
      setSelection({
        selectedRowKeys: currentPageSelectedKeys,
        selectedRows: selectedRowsFromKeys,
      });
    } else if (initialSelectedKeys.length === 0) {
      // 如果initialSelectedKeys为空，清空选择状态
      if (crossPage) {
        setAllSelectedRows(new Map());
      }
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  }, [initialSelectedKeys, data, crossPage]);

  // 当数据变化时，更新当前页面的选择状态
  useEffect(() => {
    if (crossPage && allSelectedRows.size > 0) {
      // 计算当前页面中哪些行应该被选中
      const currentPageSelectedKeys: React.Key[] = [];
      const currentPageSelectedRows: T[] = [];

      data.forEach(item => {
        if (allSelectedRows.has(item.id)) {
          currentPageSelectedKeys.push(item.id);
          currentPageSelectedRows.push(item);
        }
      });

      setSelection({
        selectedRowKeys: currentPageSelectedKeys,
        selectedRows: currentPageSelectedRows,
      });
    }
  }, [data, crossPage, allSelectedRows]);

  return {
    selection,
    allSelectedRows,
    rowSelection,
    clearSelection,
    selectAll,
    unselectAll,
    getSelectedRows,
    getSelectedKeys,
    getSelectedCount,
  };
}
